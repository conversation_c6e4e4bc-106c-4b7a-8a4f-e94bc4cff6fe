using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using CTrue.FsConnect;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Service for reading current weather conditions from MSFS
    /// </summary>
    public class WeatherService
    {
        private readonly IFsConnect _fsConnect;
        private int _weatherDefinitionId;
        private int _weatherRequestId;
        private WeatherData _currentWeather;
        private readonly object _weatherLock = new object();

        public WeatherService(IFsConnect fsConnect)
        {
            _fsConnect = fsConnect;
            InitializeWeatherDataDefinition();
        }

        /// <summary>
        /// Get current weather conditions from MSFS
        /// </summary>
        public WeatherData GetCurrentWeather()
        {
            lock (_weatherLock)
            {
                // Request fresh weather data
                _fsConnect.RequestData(_weatherRequestId, _weatherDefinitionId);
                
                // Wait a moment for the data to arrive
                System.Threading.Thread.Sleep(100);
                
                return _currentWeather ?? new WeatherData();
            }
        }

        /// <summary>
        /// Analyze current weather and identify hazards
        /// </summary>
        public List<string> AnalyzeWeatherHazards(WeatherData weather)
        {
            var hazards = new List<string>();

            // Wind hazards
            if (weather.WindSpeedKnots > 25)
                hazards.Add($"Strong winds: {weather.WindSpeedKnots:F0} knots");
            
            if (weather.WindSpeedKnots > 15)
            {
                // Calculate crosswind component for typical runway headings
                var crosswindComponent = weather.WindSpeedKnots * Math.Sin(Math.Abs(weather.WindDirectionDegrees - 270) * Math.PI / 180);
                if (crosswindComponent > 10)
                    hazards.Add($"Significant crosswind component: {crosswindComponent:F0} knots");
            }

            // Visibility hazards
            if (weather.VisibilityMeters < 1609) // Less than 1 mile
                hazards.Add($"Low visibility: {weather.VisibilityMeters / 1609:F1} miles");
            else if (weather.VisibilityMeters < 4828) // Less than 3 miles
                hazards.Add($"Reduced visibility: {weather.VisibilityMeters / 1609:F1} miles");

            // Precipitation hazards
            if (weather.PrecipitationState > 0)
            {
                var precipType = weather.PrecipitationState switch
                {
                    1 => "Light precipitation",
                    2 => "Moderate precipitation", 
                    3 => "Heavy precipitation",
                    _ => "Precipitation"
                };
                hazards.Add(precipType);
            }

            // Cloud hazards
            if (weather.InCloud)
                hazards.Add("Currently in cloud - IMC conditions");

            // Temperature hazards
            if (weather.TemperatureCelsius > 35)
                hazards.Add($"High temperature affecting performance: {weather.TemperatureCelsius:F0}°C");
            else if (weather.TemperatureCelsius < -20)
                hazards.Add($"Extreme cold conditions: {weather.TemperatureCelsius:F0}°C");

            // Pressure hazards
            if (weather.BarometricPressureInHg < 29.00)
                hazards.Add($"Low pressure system: {weather.BarometricPressureInHg:F2} inHg");
            else if (weather.BarometricPressureInHg > 30.50)
                hazards.Add($"High pressure system: {weather.BarometricPressureInHg:F2} inHg");

            return hazards;
        }

        /// <summary>
        /// Get weather description for briefing
        /// </summary>
        public string GetWeatherDescription(WeatherData weather)
        {
            var description = $"Wind: {weather.WindDirectionDegrees:F0}° at {weather.WindSpeedKnots:F0} knots, ";
            description += $"Visibility: {weather.VisibilityMeters / 1609:F1} miles, ";
            description += $"Temperature: {weather.TemperatureCelsius:F0}°C, ";
            description += $"Pressure: {weather.BarometricPressureInHg:F2} inHg";

            if (weather.PrecipitationState > 0)
                description += ", Precipitation present";

            if (weather.InCloud)
                description += ", In cloud";

            return description;
        }

        private void InitializeWeatherDataDefinition()
        {
            try
            {
                _weatherDefinitionId = _fsConnect.RegisterDataDefinition<WeatherSimVars>();
                _weatherRequestId = _weatherDefinitionId + 1000; // Offset to avoid conflicts
                
                _fsConnect.FsDataReceived += OnWeatherDataReceived;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize weather data definition: {ex.Message}");
            }
        }

        private void OnWeatherDataReceived(object sender, FsDataReceivedEventArgs e)
        {
            try
            {
                if (e.RequestId == _weatherRequestId && e.Data?.Count > 0)
                {
                    var weatherVars = (WeatherSimVars)e.Data[0];
                    
                    lock (_weatherLock)
                    {
                        _currentWeather = new WeatherData
                        {
                            WindSpeedKnots = weatherVars.WindSpeedKnots,
                            WindDirectionDegrees = weatherVars.WindDirectionDegrees,
                            VisibilityMeters = weatherVars.VisibilityMeters,
                            TemperatureCelsius = weatherVars.TemperatureCelsius,
                            BarometricPressureInHg = weatherVars.BarometricPressureInHg,
                            PrecipitationState = (int)weatherVars.PrecipitationState,
                            InCloud = weatherVars.InCloud > 0
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing weather data: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// SimConnect structure for weather variables
    /// </summary>
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi, Pack = 1)]
    public struct WeatherSimVars
    {
        [SimVar(NameId = FsSimVar.AmbientWindVelocity, UnitId = FsUnit.Knot)]
        public double WindSpeedKnots;

        [SimVar(NameId = FsSimVar.AmbientWindDirection, UnitId = FsUnit.Degree)]
        public double WindDirectionDegrees;

        [SimVar(NameId = FsSimVar.AmbientVisibility, UnitId = FsUnit.Meter)]
        public double VisibilityMeters;

        [SimVar(NameId = FsSimVar.AmbientTemperature, UnitId = FsUnit.Celsius)]
        public double TemperatureCelsius;

        [SimVar(NameId = FsSimVar.BarometerPressure, UnitId = FsUnit.InchesOfMercury)]
        public double BarometricPressureInHg;

        [SimVar(NameId = FsSimVar.AmbientPrecipState, UnitId = FsUnit.Number)]
        public double PrecipitationState;

        [SimVar(NameId = FsSimVar.AmbientInCloud, UnitId = FsUnit.Bool)]
        public double InCloud;
    }

    /// <summary>
    /// Weather data model
    /// </summary>
    public class WeatherData
    {
        public double WindSpeedKnots { get; set; }
        public double WindDirectionDegrees { get; set; }
        public double VisibilityMeters { get; set; } = 16093; // Default 10 miles
        public double TemperatureCelsius { get; set; } = 15; // Default 15°C
        public double BarometricPressureInHg { get; set; } = 29.92; // Standard pressure
        public int PrecipitationState { get; set; } // 0=none, 1=light, 2=moderate, 3=heavy
        public bool InCloud { get; set; }
    }
}
