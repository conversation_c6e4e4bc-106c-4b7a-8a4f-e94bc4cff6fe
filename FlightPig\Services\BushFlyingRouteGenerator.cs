using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// LLM-powered service to generate realistic bush flying routes and adventures
    /// </summary>
    public class BushFlyingRouteGenerator
    {
        private readonly AirportService _airportService;
        private readonly TextToSpeechService _ttsService;
        private readonly WeatherService _weatherService;
        private readonly Random _random;

        public BushFlyingRouteGenerator(AirportService airportService, TextToSpeechService ttsService, WeatherService weatherService)
        {
            _airportService = airportService;
            _ttsService = ttsService;
            _weatherService = weatherService;
            _random = new Random();
        }

        /// <summary>
        /// Generate a bush flying challenge based on current aircraft position and type
        /// </summary>
        public async Task<BushFlyingChallenge> GenerateBushFlyingChallengeAsync(
            double currentLatitude, 
            double currentLongitude, 
            string aircraftType,
            BushFlyingDifficulty difficulty = BushFlyingDifficulty.Intermediate)
        {
            Console.WriteLine($"Generating bush flying challenge for {aircraftType} at difficulty {difficulty}...");

            // Find suitable bush flying airports in the region
            var nearbyAirports = await _airportService.FindNearbyAirportsAsync(
                currentLatitude, currentLongitude, 200); // 200 NM radius

            // Filter for bush flying suitable airports
            var bushAirports = FilterBushFlyingAirports(nearbyAirports, aircraftType);

            if (bushAirports.Count < 3)
            {
                // Expand search radius if not enough suitable airports
                nearbyAirports = await _airportService.FindNearbyAirportsAsync(
                    currentLatitude, currentLongitude, 500);
                bushAirports = FilterBushFlyingAirports(nearbyAirports, aircraftType);
            }

            // Generate the challenge
            var challenge = await CreateBushFlyingChallengeAsync(
                currentLatitude, currentLongitude, bushAirports, aircraftType, difficulty);

            return challenge;
        }

        /// <summary>
        /// Filter airports suitable for bush flying based on aircraft type and characteristics
        /// </summary>
        private List<Airport> FilterBushFlyingAirports(List<Airport> airports, string aircraftType)
        {
            return airports.Where(airport =>
            {
                // Prefer smaller, more remote airports for bush flying
                if (airport.Type == AirportType.International) return false;
                
                // Include suitable airport types
                var suitableTypes = new[] { 
                    AirportType.Municipal, 
                    AirportType.Private, 
                    AirportType.Regional 
                };
                
                if (!suitableTypes.Contains(airport.Type)) return false;

                // Check if aircraft is suitable for this airport
                if (!airport.SuitableAircraft.Contains(aircraftType)) return false;

                // Prefer airports with shorter runways for bush flying authenticity
                var hasShortRunway = airport.Runways.Any(r => r.LengthFeet < 5000);
                var hasGrassRunway = airport.Runways.Any(r => 
                    r.Surface.ToLower().Contains("grass") || 
                    r.Surface.ToLower().Contains("dirt") ||
                    r.Surface.ToLower().Contains("gravel"));

                // Prefer uncontrolled airports for bush flying
                var isUncontrolled = !airport.IsControlled;

                // Score airports for bush flying suitability
                var bushScore = 0;
                if (hasShortRunway) bushScore += 2;
                if (hasGrassRunway) bushScore += 3;
                if (isUncontrolled) bushScore += 2;
                if (airport.Type == AirportType.Private) bushScore += 1;
                if (airport.Type == AirportType.Municipal) bushScore += 1;

                return bushScore >= 2; // Minimum bush flying suitability
            }).ToList();
        }

        /// <summary>
        /// Create a comprehensive bush flying challenge with LLM-generated narrative
        /// </summary>
        private async Task<BushFlyingChallenge> CreateBushFlyingChallengeAsync(
            double startLat, double startLon, List<Airport> availableAirports, 
            string aircraftType, BushFlyingDifficulty difficulty)
        {
            // Select airports for the route
            var selectedAirports = SelectRouteAirports(availableAirports, difficulty);
            
            // Determine mission type and scenario
            var missionType = SelectMissionType(difficulty);
            var terrain = DetermineTerrainType(selectedAirports);
            var weather = SelectWeatherConditions(difficulty);

            // Generate LLM-powered narrative and details
            var challenge = new BushFlyingChallenge
            {
                Title = GenerateChallengeTitle(missionType, terrain, difficulty),
                Description = GenerateChallengeDescription(missionType, terrain, selectedAirports),
                Scenario = GenerateScenario(missionType, terrain, weather, difficulty),
                BackgroundStory = GenerateBackgroundStory(missionType, terrain, selectedAirports),
                Difficulty = difficulty,
                Region = DetermineRegion(selectedAirports),
                StartingAirport = selectedAirports.First().IcaoCode,
                WeatherConditions = weather.ToString(),
                TerrainType = terrain.ToString(),
                SuitableAircraft = new List<string> { aircraftType },
                RequiredSkills = GenerateRequiredSkills(difficulty, missionType),
                Challenges = GenerateChallenges(difficulty, terrain, weather),
                SurvivalElements = GenerateSurvivalElements(difficulty, terrain),
                SafetyConsiderations = GenerateSafetyConsiderations(terrain, weather, difficulty)
            };

            // Create waypoints
            challenge.Waypoints = await CreateWaypointsAsync(selectedAirports, missionType, terrain, difficulty);
            
            // Calculate totals
            challenge.TotalDistanceNM = CalculateTotalDistance(challenge.Waypoints);
            challenge.EstimatedDurationMinutes = EstimateDuration(challenge.TotalDistanceNM, aircraftType, difficulty);

            return challenge;
        }

        /// <summary>
        /// Select airports for the bush flying route based on difficulty
        /// </summary>
        private List<Airport> SelectRouteAirports(List<Airport> availableAirports, BushFlyingDifficulty difficulty)
        {
            var waypointCount = difficulty switch
            {
                BushFlyingDifficulty.Beginner => _random.Next(2, 4),
                BushFlyingDifficulty.Intermediate => _random.Next(3, 5),
                BushFlyingDifficulty.Advanced => _random.Next(4, 6),
                BushFlyingDifficulty.Expert => _random.Next(5, 7),
                BushFlyingDifficulty.Legendary => _random.Next(6, 8),
                _ => 3
            };

            // Ensure we don't exceed available airports
            waypointCount = Math.Min(waypointCount, availableAirports.Count);

            // Select diverse airports for interesting route
            var selectedAirports = new List<Airport>();
            var remainingAirports = availableAirports.ToList();

            // Always start with a suitable airport
            var startAirport = remainingAirports.OrderBy(a => _random.Next()).First();
            selectedAirports.Add(startAirport);
            remainingAirports.Remove(startAirport);

            // Select remaining airports with some distance between them
            for (int i = 1; i < waypointCount && remainingAirports.Any(); i++)
            {
                var lastAirport = selectedAirports.Last();
                
                // Prefer airports that are 20-100 NM away for good bush flying legs
                var candidateAirports = remainingAirports.Where(a =>
                {
                    var distance = CalculateDistance(lastAirport.Latitude, lastAirport.Longitude, a.Latitude, a.Longitude);
                    return distance >= 20 && distance <= 100;
                }).ToList();

                if (!candidateAirports.Any())
                    candidateAirports = remainingAirports;

                var nextAirport = candidateAirports.OrderBy(a => _random.Next()).First();
                selectedAirports.Add(nextAirport);
                remainingAirports.Remove(nextAirport);
            }

            return selectedAirports;
        }

        /// <summary>
        /// Generate mission type based on difficulty and randomization
        /// </summary>
        private BushFlyingMissionType SelectMissionType(BushFlyingDifficulty difficulty)
        {
            var missionTypes = difficulty switch
            {
                BushFlyingDifficulty.Beginner => new[] { 
                    BushFlyingMissionType.SupplyRun, 
                    BushFlyingMissionType.Tourism, 
                    BushFlyingMissionType.Training 
                },
                BushFlyingDifficulty.Intermediate => new[] { 
                    BushFlyingMissionType.SupplyRun, 
                    BushFlyingMissionType.WildlifeResearch, 
                    BushFlyingMissionType.Fishing,
                    BushFlyingMissionType.Photography 
                },
                BushFlyingDifficulty.Advanced => new[] { 
                    BushFlyingMissionType.MedicalEvacuation, 
                    BushFlyingMissionType.SearchAndRescue, 
                    BushFlyingMissionType.FireSpotting,
                    BushFlyingMissionType.Mining 
                },
                BushFlyingDifficulty.Expert => new[] { 
                    BushFlyingMissionType.SearchAndRescue, 
                    BushFlyingMissionType.Emergency, 
                    BushFlyingMissionType.Survival,
                    BushFlyingMissionType.Conservation 
                },
                BushFlyingDifficulty.Legendary => new[] { 
                    BushFlyingMissionType.Emergency, 
                    BushFlyingMissionType.Survival, 
                    BushFlyingMissionType.SearchAndRescue 
                },
                _ => new[] { BushFlyingMissionType.SupplyRun }
            };

            return missionTypes[_random.Next(missionTypes.Length)];
        }

        /// <summary>
        /// Determine terrain type based on selected airports' locations
        /// </summary>
        private BushFlyingTerrain DetermineTerrainType(List<Airport> airports)
        {
            // This would ideally use geographic data, but for now we'll use airport characteristics
            var terrainTypes = new[] { 
                BushFlyingTerrain.Forest, 
                BushFlyingTerrain.Mountains, 
                BushFlyingTerrain.Prairie,
                BushFlyingTerrain.Coastal,
                BushFlyingTerrain.River,
                BushFlyingTerrain.Lake
            };

            return terrainTypes[_random.Next(terrainTypes.Length)];
        }

        /// <summary>
        /// Select weather conditions based on difficulty
        /// </summary>
        private BushFlyingWeather SelectWeatherConditions(BushFlyingDifficulty difficulty)
        {
            var weatherOptions = difficulty switch
            {
                BushFlyingDifficulty.Beginner => new[] { 
                    BushFlyingWeather.Clear, 
                    BushFlyingWeather.PartlyCloudy 
                },
                BushFlyingDifficulty.Intermediate => new[] { 
                    BushFlyingWeather.PartlyCloudy, 
                    BushFlyingWeather.Overcast, 
                    BushFlyingWeather.LightRain 
                },
                BushFlyingDifficulty.Advanced => new[] { 
                    BushFlyingWeather.Overcast, 
                    BushFlyingWeather.HeavyRain, 
                    BushFlyingWeather.HighWinds,
                    BushFlyingWeather.Fog 
                },
                BushFlyingDifficulty.Expert => new[] { 
                    BushFlyingWeather.Thunderstorms, 
                    BushFlyingWeather.Snow, 
                    BushFlyingWeather.Turbulence,
                    BushFlyingWeather.Variable 
                },
                BushFlyingDifficulty.Legendary => new[] { 
                    BushFlyingWeather.Blizzard, 
                    BushFlyingWeather.Icing, 
                    BushFlyingWeather.Variable 
                },
                _ => new[] { BushFlyingWeather.Clear }
            };

            return weatherOptions[_random.Next(weatherOptions.Length)];
        }

        /// <summary>
        /// Generate challenge title based on mission and terrain
        /// </summary>
        private string GenerateChallengeTitle(BushFlyingMissionType mission, BushFlyingTerrain terrain, BushFlyingDifficulty difficulty)
        {
            var titleTemplates = mission switch
            {
                BushFlyingMissionType.SupplyRun => new[] {
                    $"Remote {terrain} Supply Run",
                    $"{terrain} Outpost Resupply",
                    $"Essential Supplies to {terrain} Base"
                },
                BushFlyingMissionType.MedicalEvacuation => new[] {
                    $"Emergency Medevac from {terrain}",
                    $"Critical Medical Transport - {terrain}",
                    $"Life Flight: {terrain} Rescue"
                },
                BushFlyingMissionType.SearchAndRescue => new[] {
                    $"Search and Rescue: {terrain} Mission",
                    $"Missing Aircraft - {terrain} Search",
                    $"Wilderness Rescue in {terrain}"
                },
                BushFlyingMissionType.WildlifeResearch => new[] {
                    $"{terrain} Wildlife Survey",
                    $"Research Flight: {terrain} Expedition",
                    $"Conservation Mission - {terrain}"
                },
                BushFlyingMissionType.FireSpotting => new[] {
                    $"{terrain} Fire Patrol",
                    $"Wildfire Detection - {terrain}",
                    $"Fire Spotting Mission: {terrain}"
                },
                _ => new[] { $"{terrain} Bush Flying Adventure" }
            };

            var title = titleTemplates[_random.Next(titleTemplates.Length)];

            if (difficulty >= BushFlyingDifficulty.Expert)
                title = $"EXTREME: {title}";
            else if (difficulty >= BushFlyingDifficulty.Advanced)
                title = $"ADVANCED: {title}";

            return title;
        }

        /// <summary>
        /// Generate detailed challenge description
        /// </summary>
        private string GenerateChallengeDescription(BushFlyingMissionType mission, BushFlyingTerrain terrain, List<Airport> airports)
        {
            var descriptions = mission switch
            {
                BushFlyingMissionType.SupplyRun =>
                    $"Transport essential supplies through {airports.Count} remote {terrain.ToString().ToLower()} airstrips. " +
                    $"Navigate challenging terrain while managing fuel and weather conditions.",

                BushFlyingMissionType.MedicalEvacuation =>
                    $"Time-critical medical evacuation mission across {terrain.ToString().ToLower()} terrain. " +
                    $"Every minute counts as you navigate to remote locations for emergency transport.",

                BushFlyingMissionType.SearchAndRescue =>
                    $"Search for missing persons across vast {terrain.ToString().ToLower()} wilderness. " +
                    $"Use your bush flying skills to access remote areas and coordinate rescue efforts.",

                BushFlyingMissionType.WildlifeResearch =>
                    $"Support wildlife research by accessing remote {terrain.ToString().ToLower()} locations. " +
                    $"Transport researchers and equipment while minimizing environmental impact.",

                _ => $"Multi-leg bush flying adventure through challenging {terrain.ToString().ToLower()} terrain."
            };

            return descriptions;
        }

        /// <summary>
        /// Generate immersive scenario narrative
        /// </summary>
        private string GenerateScenario(BushFlyingMissionType mission, BushFlyingTerrain terrain, BushFlyingWeather weather, BushFlyingDifficulty difficulty)
        {
            var scenarios = mission switch
            {
                BushFlyingMissionType.SupplyRun =>
                    $"A remote {terrain.ToString().ToLower()} community has been cut off due to {weather.ToString().ToLower()} conditions. " +
                    $"You're their lifeline, carrying essential supplies including medical equipment, food, and fuel. " +
                    $"The weather is challenging, and some of the airstrips are barely maintained.",

                BushFlyingMissionType.MedicalEvacuation =>
                    $"A serious accident has occurred at a remote {terrain.ToString().ToLower()} research station. " +
                    $"Despite {weather.ToString().ToLower()} conditions, you must reach the injured person quickly. " +
                    $"The nearest hospital is hours away by ground, but you can make it in minutes by air.",

                BushFlyingMissionType.SearchAndRescue =>
                    $"A small aircraft went missing three days ago in the {terrain.ToString().ToLower()} wilderness. " +
                    $"Search teams have been grounded due to {weather.ToString().ToLower()} conditions, but you're experienced enough to fly. " +
                    $"Time is running out, and you're their best hope.",

                _ => $"You're embarking on a challenging bush flying mission through {terrain.ToString().ToLower()} terrain " +
                     $"with {weather.ToString().ToLower()} conditions testing your skills."
            };

            return scenarios;
        }

        /// <summary>
        /// Generate background story for immersion
        /// </summary>
        private string GenerateBackgroundStory(BushFlyingMissionType mission, BushFlyingTerrain terrain, List<Airport> airports)
        {
            var stories = mission switch
            {
                BushFlyingMissionType.SupplyRun =>
                    $"You're a bush pilot working for a remote supply company. Your route takes you through some of the most " +
                    $"challenging {terrain.ToString().ToLower()} terrain, serving communities that depend on your deliveries. " +
                    $"Each airstrip has its own personality and challenges - from short grass strips to mountain plateaus.",

                BushFlyingMissionType.MedicalEvacuation =>
                    $"As a volunteer pilot for the regional air ambulance service, you've saved countless lives. " +
                    $"Your intimate knowledge of {terrain.ToString().ToLower()} flying and ability to land in challenging conditions " +
                    $"makes you the go-to pilot when others can't fly.",

                BushFlyingMissionType.SearchAndRescue =>
                    $"You're part of the volunteer search and rescue team, known for your ability to access remote areas " +
                    $"that ground teams can't reach. Your experience in {terrain.ToString().ToLower()} flying has made you " +
                    $"invaluable in emergency situations.",

                _ => $"You're an experienced bush pilot with thousands of hours flying in {terrain.ToString().ToLower()} conditions. " +
                     $"This mission will test all your skills and experience."
            };

            return stories;
        }

        /// <summary>
        /// Calculate distance between two points in nautical miles
        /// </summary>
        private double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
        {
            var R = 3440.065; // Earth's radius in nautical miles
            var dLat = (lat2 - lat1) * Math.PI / 180;
            var dLon = (lon2 - lon1) * Math.PI / 180;
            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(lat1 * Math.PI / 180) * Math.Cos(lat2 * Math.PI / 180) *
                    Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            return R * c;
        }

        /// <summary>
        /// Generate required skills based on difficulty and mission
        /// </summary>
        private List<string> GenerateRequiredSkills(BushFlyingDifficulty difficulty, BushFlyingMissionType mission)
        {
            var skills = new List<string> { "Short field landings", "Terrain navigation", "Fuel management" };

            if (difficulty >= BushFlyingDifficulty.Intermediate)
                skills.AddRange(new[] { "Weather assessment", "Emergency procedures" });

            if (difficulty >= BushFlyingDifficulty.Advanced)
                skills.AddRange(new[] { "Mountain flying", "Crosswind landings", "Survival skills" });

            if (difficulty >= BushFlyingDifficulty.Expert)
                skills.AddRange(new[] { "Extreme weather flying", "Emergency navigation", "Risk management" });

            return skills;
        }

        /// <summary>
        /// Generate challenges based on conditions
        /// </summary>
        private List<string> GenerateChallenges(BushFlyingDifficulty difficulty, BushFlyingTerrain terrain, BushFlyingWeather weather)
        {
            var challenges = new List<string>();

            // Terrain-based challenges
            switch (terrain)
            {
                case BushFlyingTerrain.Mountains:
                    challenges.AddRange(new[] { "High altitude performance", "Mountain wave turbulence", "Density altitude effects" });
                    break;
                case BushFlyingTerrain.Forest:
                    challenges.AddRange(new[] { "Limited emergency landing options", "Navigation without landmarks", "Short forest strips" });
                    break;
                case BushFlyingTerrain.Desert:
                    challenges.AddRange(new[] { "Heat effects on performance", "Limited navigation aids", "Dust and visibility" });
                    break;
            }

            // Weather-based challenges
            if (weather == BushFlyingWeather.HighWinds)
                challenges.Add("Strong crosswind landings");
            if (weather == BushFlyingWeather.Fog)
                challenges.Add("Low visibility operations");

            // Difficulty-based challenges
            if (difficulty >= BushFlyingDifficulty.Advanced)
                challenges.AddRange(new[] { "Fuel planning precision", "Emergency decision making" });

            return challenges;
        }

        /// <summary>
        /// Generate survival elements for immersion
        /// </summary>
        private List<string> GenerateSurvivalElements(BushFlyingDifficulty difficulty, BushFlyingTerrain terrain)
        {
            var elements = new List<string>();

            if (difficulty >= BushFlyingDifficulty.Intermediate)
                elements.AddRange(new[] { "Emergency supplies check", "Communication protocols" });

            if (difficulty >= BushFlyingDifficulty.Advanced)
                elements.AddRange(new[] { "Survival kit inventory", "Emergency shelter knowledge", "Water procurement" });

            if (difficulty >= BushFlyingDifficulty.Expert)
                elements.AddRange(new[] { "Wilderness first aid", "Signaling techniques", "Food procurement" });

            return elements;
        }

        /// <summary>
        /// Generate safety considerations
        /// </summary>
        private List<string> GenerateSafetyConsiderations(BushFlyingTerrain terrain, BushFlyingWeather weather, BushFlyingDifficulty difficulty)
        {
            var safety = new List<string>
            {
                "Pre-flight planning critical",
                "Weather monitoring essential",
                "Fuel reserves mandatory"
            };

            if (terrain == BushFlyingTerrain.Mountains)
                safety.AddRange(new[] { "Density altitude calculations", "Escape route planning" });

            if (weather == BushFlyingWeather.Variable)
                safety.Add("Continuous weather assessment required");

            if (difficulty >= BushFlyingDifficulty.Advanced)
                safety.AddRange(new[] { "Emergency equipment required", "Flight plan filing recommended" });

            return safety;
        }

        /// <summary>
        /// Create detailed waypoints for the route
        /// </summary>
        private Task<List<BushFlyingWaypoint>> CreateWaypointsAsync(
            List<Airport> airports, BushFlyingMissionType mission, BushFlyingTerrain terrain, BushFlyingDifficulty difficulty)
        {
            var waypoints = new List<BushFlyingWaypoint>();

            for (int i = 0; i < airports.Count; i++)
            {
                var airport = airports[i];
                var waypoint = new BushFlyingWaypoint
                {
                    SequenceNumber = i + 1,
                    Name = airport.Name,
                    Description = BushFlyingRouteGeneratorHelpers.GenerateWaypointDescription(airport, mission, i, airports.Count),
                    Latitude = airport.Latitude,
                    Longitude = airport.Longitude,
                    ElevationFeet = airport.ElevationFeet,
                    Type = BushFlyingRouteGeneratorHelpers.DetermineWaypointType(i, airports.Count, mission),
                    Airport = airport,
                    Purpose = BushFlyingRouteGeneratorHelpers.GenerateWaypointPurpose(mission, i, airports.Count),
                    TerrainDescription = BushFlyingRouteGeneratorHelpers.GenerateTerrainDescription(terrain, airport),
                    ApproachNotes = BushFlyingRouteGeneratorHelpers.GenerateApproachNotes(airport, terrain, difficulty),
                    LandingNotes = BushFlyingRouteGeneratorHelpers.GenerateLandingNotes(airport, difficulty),
                    LocalChallenges = BushFlyingRouteGeneratorHelpers.GenerateLocalChallenges(airport, terrain, difficulty),
                    NavigationNotes = BushFlyingRouteGeneratorHelpers.GenerateNavigationNotes(airport, terrain),
                    LocalHazards = GenerateCurrentHazards(terrain, difficulty),
                    WeatherConsiderations = GenerateCurrentWeatherConsiderations(),
                    FuelConsiderations = BushFlyingRouteGeneratorHelpers.GenerateFuelConsiderations(i, airports.Count, difficulty)
                };

                if (i > 0)
                {
                    var prevAirport = airports[i - 1];
                    waypoint.DistanceFromPreviousNM = CalculateDistance(
                        prevAirport.Latitude, prevAirport.Longitude,
                        airport.Latitude, airport.Longitude);
                    waypoint.HeadingFromPrevious = CalculateHeading(
                        prevAirport.Latitude, prevAirport.Longitude,
                        airport.Latitude, airport.Longitude);
                }

                waypoints.Add(waypoint);
            }

            return Task.FromResult(waypoints);
        }

        /// <summary>
        /// Calculate total route distance
        /// </summary>
        private double CalculateTotalDistance(List<BushFlyingWaypoint> waypoints)
        {
            return waypoints.Sum(w => w.DistanceFromPreviousNM);
        }

        /// <summary>
        /// Estimate flight duration based on distance and conditions
        /// </summary>
        private double EstimateDuration(double distanceNM, string aircraftType, BushFlyingDifficulty difficulty)
        {
            var baseSpeed = aircraftType switch
            {
                "General Aviation" => 120, // knots
                "Turboprop" => 180,
                "Helicopter" => 100,
                _ => 120
            };

            // Reduce speed for bush flying conditions
            var bushFlyingSpeed = baseSpeed * 0.7; // Slower for terrain and conditions

            // Add time for difficulty
            var difficultyMultiplier = difficulty switch
            {
                BushFlyingDifficulty.Beginner => 1.0,
                BushFlyingDifficulty.Intermediate => 1.2,
                BushFlyingDifficulty.Advanced => 1.4,
                BushFlyingDifficulty.Expert => 1.6,
                BushFlyingDifficulty.Legendary => 1.8,
                _ => 1.0
            };

            var flightTimeHours = (distanceNM / bushFlyingSpeed) * difficultyMultiplier;
            return flightTimeHours * 60; // Convert to minutes
        }

        /// <summary>
        /// Calculate heading between two points
        /// </summary>
        private double CalculateHeading(double lat1, double lon1, double lat2, double lon2)
        {
            var dLon = (lon2 - lon1) * Math.PI / 180;
            var lat1Rad = lat1 * Math.PI / 180;
            var lat2Rad = lat2 * Math.PI / 180;

            var y = Math.Sin(dLon) * Math.Cos(lat2Rad);
            var x = Math.Cos(lat1Rad) * Math.Sin(lat2Rad) - Math.Sin(lat1Rad) * Math.Cos(lat2Rad) * Math.Cos(dLon);

            var heading = Math.Atan2(y, x) * 180 / Math.PI;
            return (heading + 360) % 360;
        }

        /// <summary>
        /// Determine region based on airports
        /// </summary>
        private string DetermineRegion(List<Airport> airports)
        {
            // Use the country of the first airport as the region
            return airports.FirstOrDefault()?.Country ?? "Unknown Region";
        }

        /// <summary>
        /// Generate current hazards based on real weather and terrain
        /// </summary>
        private List<string> GenerateCurrentHazards(BushFlyingTerrain terrain, BushFlyingDifficulty difficulty)
        {
            var hazards = new List<string>();

            // Add terrain-based static hazards
            hazards.AddRange(BushFlyingRouteGeneratorHelpers.GenerateTerrainHazards(terrain, difficulty));

            // Add current weather-based hazards
            try
            {
                var currentWeather = _weatherService.GetCurrentWeather();
                var weatherHazards = _weatherService.AnalyzeWeatherHazards(currentWeather);
                hazards.AddRange(weatherHazards);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Could not get current weather for hazard analysis: {ex.Message}");
                hazards.Add("Weather conditions unknown - exercise caution");
            }

            return hazards;
        }

        /// <summary>
        /// Generate weather considerations based on current MSFS weather
        /// </summary>
        private string GenerateCurrentWeatherConsiderations()
        {
            try
            {
                var currentWeather = _weatherService.GetCurrentWeather();
                var description = _weatherService.GetWeatherDescription(currentWeather);

                return $"Current conditions: {description}. Monitor weather throughout flight.";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Could not get current weather: {ex.Message}");
                return "Weather conditions unknown - monitor MSFS weather systems and exercise caution";
            }
        }
    }
}
