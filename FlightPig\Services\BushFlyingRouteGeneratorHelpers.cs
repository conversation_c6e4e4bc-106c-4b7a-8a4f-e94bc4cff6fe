using System;
using System.Collections.Generic;
using System.Linq;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Helper methods for bush flying route generation
    /// </summary>
    public static class BushFlyingRouteGeneratorHelpers
    {
        /// <summary>
        /// Generate waypoint description based on mission and position
        /// </summary>
        public static string GenerateWaypointDescription(Airport airport, BushFlyingMissionType mission, int index, int total)
        {
            if (index == 0)
                return $"Starting point for your {mission.ToString().ToLower().Replace("_", " ")} mission";
            
            if (index == total - 1)
                return $"Final destination - mission completion point";

            return mission switch
            {
                BushFlyingMissionType.SupplyRun => $"Supply drop point {index + 1} - deliver essential cargo",
                BushFlyingMissionType.MedicalEvacuation => $"Medical pickup/dropoff point {index + 1}",
                BushFlyingMissionType.SearchAndRescue => $"Search grid checkpoint {index + 1}",
                BushFlyingMissionType.WildlifeResearch => $"Research station {index + 1} - wildlife observation point",
                BushFlyingMissionType.FireSpotting => $"Fire patrol checkpoint {index + 1}",
                _ => $"Mission waypoint {index + 1}"
            };
        }

        /// <summary>
        /// Determine waypoint type based on position and mission
        /// </summary>
        public static BushFlyingWaypointType DetermineWaypointType(int index, int total, BushFlyingMissionType mission)
        {
            if (index == 0) return BushFlyingWaypointType.StartingPoint;
            if (index == total - 1) return BushFlyingWaypointType.Destination;

            return mission switch
            {
                BushFlyingMissionType.SupplyRun => BushFlyingWaypointType.SupplyDrop,
                BushFlyingMissionType.MedicalEvacuation => BushFlyingWaypointType.Rescue,
                BushFlyingMissionType.SearchAndRescue => BushFlyingWaypointType.Checkpoint,
                BushFlyingMissionType.WildlifeResearch => BushFlyingWaypointType.WildlifeObservation,
                BushFlyingMissionType.FireSpotting => BushFlyingWaypointType.WeatherCheck,
                _ => BushFlyingWaypointType.Checkpoint
            };
        }

        /// <summary>
        /// Generate waypoint purpose description
        /// </summary>
        public static string GenerateWaypointPurpose(BushFlyingMissionType mission, int index, int total)
        {
            if (index == 0) return "Mission departure point";
            if (index == total - 1) return "Mission completion";

            return mission switch
            {
                BushFlyingMissionType.SupplyRun => "Deliver supplies to remote community",
                BushFlyingMissionType.MedicalEvacuation => "Emergency medical transport",
                BushFlyingMissionType.SearchAndRescue => "Search for missing persons/aircraft",
                BushFlyingMissionType.WildlifeResearch => "Support wildlife research activities",
                BushFlyingMissionType.FireSpotting => "Monitor for wildfire activity",
                BushFlyingMissionType.Exploration => "Explore uncharted territory",
                BushFlyingMissionType.Tourism => "Scenic bush flying experience",
                _ => "Complete mission objective"
            };
        }

        /// <summary>
        /// Generate terrain description for waypoint
        /// </summary>
        public static string GenerateTerrainDescription(BushFlyingTerrain terrain, Airport airport)
        {
            var baseDescription = terrain switch
            {
                BushFlyingTerrain.Mountains => "Mountainous terrain with steep valleys and ridges",
                BushFlyingTerrain.Forest => "Dense forest with limited clearings",
                BushFlyingTerrain.Tundra => "Open tundra with permafrost and minimal vegetation",
                BushFlyingTerrain.Desert => "Arid desert with sparse landmarks",
                BushFlyingTerrain.Swamp => "Wetlands with challenging visibility",
                BushFlyingTerrain.Coastal => "Coastal area with water crossings",
                BushFlyingTerrain.Prairie => "Open grasslands with few obstacles",
                BushFlyingTerrain.Canyon => "Deep canyons and mesa formations",
                BushFlyingTerrain.Lake => "Lake country with numerous water bodies",
                _ => "Varied terrain requiring careful navigation"
            };

            return $"{baseDescription}. Airport elevation: {airport.ElevationFeet:F0} feet.";
        }

        /// <summary>
        /// Generate approach notes based on airport and conditions
        /// </summary>
        public static string GenerateApproachNotes(Airport airport, BushFlyingTerrain terrain, BushFlyingDifficulty difficulty)
        {
            var notes = new List<string>();

            // Runway-based notes
            var runway = airport.Runways.FirstOrDefault();
            if (runway != null)
            {
                if (runway.LengthFeet < 3000)
                    notes.Add("Short field approach required");
                
                if (runway.Surface.ToLower().Contains("grass"))
                    notes.Add("Grass runway - check for soft spots");
                
                if (runway.Surface.ToLower().Contains("gravel"))
                    notes.Add("Gravel surface - watch for prop damage");
            }

            // Terrain-based notes
            switch (terrain)
            {
                case BushFlyingTerrain.Mountains:
                    notes.Add("Watch for mountain wave and downdrafts");
                    notes.Add("High density altitude affects performance");
                    break;
                case BushFlyingTerrain.Forest:
                    notes.Add("Steep approach over trees required");
                    notes.Add("Limited go-around options");
                    break;
                case BushFlyingTerrain.Canyon:
                    notes.Add("Box canyon - ensure adequate climb performance");
                    break;
            }

            // Difficulty-based notes
            if (difficulty >= BushFlyingDifficulty.Advanced)
                notes.Add("Challenging conditions - use extreme caution");

            return string.Join(". ", notes) + ".";
        }

        /// <summary>
        /// Generate landing notes
        /// </summary>
        public static string GenerateLandingNotes(Airport airport, BushFlyingDifficulty difficulty)
        {
            var notes = new List<string>();
            var runway = airport.Runways.FirstOrDefault();

            if (runway != null)
            {
                if (runway.LengthFeet < 2500)
                    notes.Add("Very short runway - maximum braking required");
                else if (runway.LengthFeet < 4000)
                    notes.Add("Short runway - plan for short field landing");

                if (!runway.HasLights)
                    notes.Add("No runway lighting - daylight operations only");

                if (runway.Surface.ToLower().Contains("grass"))
                    notes.Add("Soft field technique recommended");
            }

            if (!airport.IsControlled)
                notes.Add("Uncontrolled airport - monitor CTAF frequency");

            if (difficulty >= BushFlyingDifficulty.Expert)
                notes.Add("Extreme conditions - consider alternate if unsafe");

            return string.Join(". ", notes) + ".";
        }

        /// <summary>
        /// Generate local challenges for waypoint
        /// </summary>
        public static List<string> GenerateLocalChallenges(Airport airport, BushFlyingTerrain terrain, BushFlyingDifficulty difficulty)
        {
            var challenges = new List<string>();

            // Airport-specific challenges
            var runway = airport.Runways.FirstOrDefault();
            if (runway?.LengthFeet < 3000)
                challenges.Add("Short field performance");

            if (airport.ElevationFeet > 5000)
                challenges.Add("High altitude operations");

            // Terrain challenges
            switch (terrain)
            {
                case BushFlyingTerrain.Mountains:
                    challenges.AddRange(new[] { "Mountain wave turbulence", "Density altitude effects" });
                    break;
                case BushFlyingTerrain.Forest:
                    challenges.AddRange(new[] { "Obstacle clearance", "Limited emergency options" });
                    break;
                case BushFlyingTerrain.Desert:
                    challenges.AddRange(new[] { "Heat effects", "Limited navigation aids" });
                    break;
            }

            // Difficulty-based challenges
            if (difficulty >= BushFlyingDifficulty.Advanced)
                challenges.Add("Weather deterioration possible");

            return challenges;
        }

        /// <summary>
        /// Generate navigation notes
        /// </summary>
        public static List<string> GenerateNavigationNotes(Airport airport, BushFlyingTerrain terrain)
        {
            var notes = new List<string>();

            switch (terrain)
            {
                case BushFlyingTerrain.Forest:
                    notes.AddRange(new[] { "Limited visual landmarks", "Follow river systems when possible" });
                    break;
                case BushFlyingTerrain.Desert:
                    notes.AddRange(new[] { "Sparse landmarks", "Use GPS navigation primarily" });
                    break;
                case BushFlyingTerrain.Mountains:
                    notes.AddRange(new[] { "Use ridge lines for navigation", "Maintain safe altitude above terrain" });
                    break;
                case BushFlyingTerrain.Lake:
                    notes.AddRange(new[] { "Use lake patterns for navigation", "Water reflections may affect visibility" });
                    break;
            }

            if (!string.IsNullOrEmpty(airport.IataCode))
                notes.Add($"Airport identifier: {airport.IataCode}");

            return notes;
        }

        /// <summary>
        /// Generate terrain-based hazard considerations (static hazards that don't depend on current weather)
        /// </summary>
        public static List<string> GenerateTerrainHazards(BushFlyingTerrain terrain, BushFlyingDifficulty difficulty)
        {
            var hazards = new List<string>();

            switch (terrain)
            {
                case BushFlyingTerrain.Mountains:
                    hazards.AddRange(new[] { "Mountain wave turbulence potential", "Density altitude effects", "Terrain clearance critical" });
                    break;
                case BushFlyingTerrain.Forest:
                    hazards.AddRange(new[] { "Wildlife on runway possible", "Limited emergency landing options", "Obstacle clearance required" });
                    break;
                case BushFlyingTerrain.Desert:
                    hazards.AddRange(new[] { "Heat effects on performance", "Limited navigation aids", "Dust devil potential" });
                    break;
                case BushFlyingTerrain.Swamp:
                    hazards.AddRange(new[] { "Soft runway conditions possible", "Wildlife activity", "Moisture effects" });
                    break;
                case BushFlyingTerrain.Coastal:
                    hazards.AddRange(new[] { "Salt air corrosion", "Tide effects on beach strips", "Marine layer potential" });
                    break;
                case BushFlyingTerrain.Tundra:
                    hazards.AddRange(new[] { "Permafrost runway conditions", "Limited infrastructure", "Extreme cold effects" });
                    break;
            }

            if (difficulty >= BushFlyingDifficulty.Expert)
                hazards.AddRange(new[] { "Equipment failure scenarios", "Emergency landing challenges" });

            return hazards;
        }

        /// <summary>
        /// Generate weather considerations
        /// </summary>
        public static string GenerateWeatherConsiderations(BushFlyingDifficulty difficulty)
        {
            return difficulty switch
            {
                BushFlyingDifficulty.Beginner => "Monitor weather but conditions expected to remain stable",
                BushFlyingDifficulty.Intermediate => "Weather may change - monitor conditions continuously",
                BushFlyingDifficulty.Advanced => "Challenging weather possible - be prepared for diversions",
                BushFlyingDifficulty.Expert => "Severe weather likely - extreme caution required",
                BushFlyingDifficulty.Legendary => "Extreme weather conditions - survival skills essential",
                _ => "Monitor weather conditions"
            };
        }

        /// <summary>
        /// Generate fuel considerations
        /// </summary>
        public static string GenerateFuelConsiderations(int waypointIndex, int totalWaypoints, BushFlyingDifficulty difficulty)
        {
            if (waypointIndex == 0)
                return "Ensure full fuel load for mission";

            var midpoint = totalWaypoints / 2;
            if (waypointIndex == midpoint)
                return "Consider refueling if available - long legs ahead";

            if (waypointIndex == totalWaypoints - 1)
                return "Monitor fuel reserves for safe completion";

            return difficulty >= BushFlyingDifficulty.Advanced 
                ? "Fuel planning critical - no margin for error"
                : "Monitor fuel consumption carefully";
        }
    }
}
