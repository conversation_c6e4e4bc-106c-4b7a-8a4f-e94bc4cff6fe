using System;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace FlightPig.Services
{
    /// <summary>
    /// Service for automatically installing and managing Ollama
    /// </summary>
    public class OllamaInstaller
    {
        private readonly HttpClient _httpClient;
        private const string OLLAMA_DOWNLOAD_URL_WINDOWS = "https://ollama.com/download/OllamaSetup.exe";
        private const string OLLAMA_DOWNLOAD_URL_MAC = "https://ollama.com/download/Ollama-darwin.zip";
        private const string OLLAMA_DOWNLOAD_URL_LINUX = "https://ollama.com/install.sh";

        public OllamaInstaller(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        /// <summary>
        /// Check if Ollama is installed and running
        /// </summary>
        public async Task<bool> IsOllamaAvailableAsync()
        {
            try
            {
                // Try to connect to Ollama API
                var response = await _httpClient.GetAsync("http://localhost:11434/api/version");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Check if Ollama is installed (but maybe not running)
        /// </summary>
        public bool IsOllamaInstalled()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    // Check if ollama.exe exists in common installation paths
                    var paths = new[]
                    {
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Programs", "Ollama", "ollama.exe"),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "Ollama", "ollama.exe"),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Ollama", "ollama.exe")
                    };

                    foreach (var path in paths)
                    {
                        if (File.Exists(path)) return true;
                    }

                    // Check if ollama is in PATH
                    try
                    {
                        var process = Process.Start(new ProcessStartInfo
                        {
                            FileName = "ollama",
                            Arguments = "--version",
                            UseShellExecute = false,
                            RedirectStandardOutput = true,
                            CreateNoWindow = true
                        });
                        process?.WaitForExit(5000);
                        return process?.ExitCode == 0;
                    }
                    catch
                    {
                        return false;
                    }
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    return File.Exists("/usr/local/bin/ollama") || File.Exists("/opt/homebrew/bin/ollama");
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    return File.Exists("/usr/local/bin/ollama") || File.Exists("/usr/bin/ollama");
                }
            }
            catch
            {
                // Ignore errors
            }

            return false;
        }

        /// <summary>
        /// Try to start Ollama if it's installed but not running
        /// </summary>
        public async Task<bool> TryStartOllamaAsync()
        {
            try
            {
                Console.WriteLine("Attempting to start Ollama...");

                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    // First try to start Ollama app (if installed via installer)
                    try
                    {
                        var ollamaAppPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Programs", "Ollama", "Ollama.exe");
                        if (File.Exists(ollamaAppPath))
                        {
                            Process.Start(new ProcessStartInfo
                            {
                                FileName = ollamaAppPath,
                                UseShellExecute = true,
                                CreateNoWindow = false
                            });

                            Console.WriteLine("Started Ollama application...");
                            await Task.Delay(5000); // Give it more time to start

                            if (await IsOllamaAvailableAsync())
                                return true;
                        }
                    }
                    catch
                    {
                        // Ignore and try command line approach
                    }

                    // Try command line approach
                    var process = Process.Start(new ProcessStartInfo
                    {
                        FileName = "ollama",
                        Arguments = "serve",
                        UseShellExecute = false,
                        CreateNoWindow = true
                    });

                    await Task.Delay(3000);
                    return await IsOllamaAvailableAsync();
                }
                else
                {
                    // For macOS and Linux, try to start ollama serve
                    var process = Process.Start(new ProcessStartInfo
                    {
                        FileName = "ollama",
                        Arguments = "serve",
                        UseShellExecute = false,
                        CreateNoWindow = true
                    });

                    await Task.Delay(3000);
                    return await IsOllamaAvailableAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to start Ollama: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Download and install Ollama
        /// </summary>
        public async Task<bool> InstallOllamaAsync()
        {
            try
            {
                Console.WriteLine("Downloading Ollama installer...");

                string downloadUrl;
                string fileName;

                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    downloadUrl = OLLAMA_DOWNLOAD_URL_WINDOWS;
                    fileName = "OllamaSetup.exe";
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    downloadUrl = OLLAMA_DOWNLOAD_URL_MAC;
                    fileName = "Ollama-darwin.zip";
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    // For Linux, we'll use the install script
                    return await InstallOllamaLinuxAsync();
                }
                else
                {
                    Console.WriteLine("Unsupported operating system for automatic Ollama installation.");
                    return false;
                }

                var tempPath = Path.Combine(Path.GetTempPath(), fileName);

                // Download the installer
                using (var response = await _httpClient.GetAsync(downloadUrl))
                {
                    response.EnsureSuccessStatusCode();
                    using (var fileStream = File.Create(tempPath))
                    {
                        await response.Content.CopyToAsync(fileStream);
                    }
                }

                Console.WriteLine($"Downloaded installer to: {tempPath}");

                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    // Run the Windows installer
                    var process = Process.Start(new ProcessStartInfo
                    {
                        FileName = tempPath,
                        UseShellExecute = true,
                        Verb = "runas" // Run as administrator
                    });

                    Console.WriteLine("Ollama installer started. Please follow the installation wizard.");
                    Console.WriteLine("Press any key after installation is complete...");
                    Console.ReadKey();

                    // Clean up
                    try { File.Delete(tempPath); } catch { }

                    // Check if installation was successful
                    return IsOllamaInstalled();
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to install Ollama: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Install Ollama on Linux using the official script
        /// </summary>
        private async Task<bool> InstallOllamaLinuxAsync()
        {
            try
            {
                Console.WriteLine("Installing Ollama on Linux...");

                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "bash",
                    Arguments = "-c \"curl -fsSL https://ollama.com/install.sh | sh\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                });

                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    Console.WriteLine("Ollama installed successfully on Linux.");
                    return true;
                }
                else
                {
                    Console.WriteLine("Failed to install Ollama on Linux.");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to install Ollama on Linux: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Show user-friendly installation guidance
        /// </summary>
        public async Task<bool> GuideUserThroughInstallationAsync()
        {
            Console.WriteLine();
            Console.WriteLine("=== OLLAMA INSTALLATION REQUIRED ===");
            Console.WriteLine();
            Console.WriteLine("FlightPig requires Ollama to generate AI-powered missions and tours.");
            Console.WriteLine("Ollama is a free, open-source tool for running large language models locally.");
            Console.WriteLine();

            // Check if already installed but not running
            if (IsOllamaInstalled())
            {
                Console.WriteLine("Ollama is installed but not running. Attempting to start it...");
                if (await TryStartOllamaAsync())
                {
                    Console.WriteLine("✓ Ollama started successfully!");
                    return true;
                }
                else
                {
                    Console.WriteLine("Failed to start Ollama automatically.");
                    Console.WriteLine("Please try running 'ollama serve' in a command prompt/terminal.");
                    return false;
                }
            }

            Console.WriteLine("Would you like to download and install Ollama automatically? (y/n)");
            var response = Console.ReadKey();
            Console.WriteLine();

            if (response.KeyChar == 'y' || response.KeyChar == 'Y')
            {
                var installed = await InstallOllamaAsync();
                if (installed)
                {
                    Console.WriteLine("Ollama installation completed. Attempting to start...");
                    await Task.Delay(2000);
                    
                    if (await TryStartOllamaAsync())
                    {
                        Console.WriteLine("✓ Ollama is now running!");
                        return true;
                    }
                    else
                    {
                        Console.WriteLine("Ollama installed but failed to start automatically.");
                        Console.WriteLine("Please try running 'ollama serve' in a command prompt/terminal.");
                        return false;
                    }
                }
                else
                {
                    Console.WriteLine("Automatic installation failed.");
                    ShowManualInstallationInstructions();
                    return false;
                }
            }
            else
            {
                ShowManualInstallationInstructions();
                return false;
            }
        }

        /// <summary>
        /// Show manual installation instructions
        /// </summary>
        private void ShowManualInstallationInstructions()
        {
            Console.WriteLine();
            Console.WriteLine("=== MANUAL INSTALLATION INSTRUCTIONS ===");
            Console.WriteLine();
            Console.WriteLine("1. Visit: https://ollama.com/download");
            Console.WriteLine("2. Download the installer for your operating system");
            Console.WriteLine("3. Run the installer and follow the setup wizard");
            Console.WriteLine("4. After installation, open a command prompt/terminal and run: ollama serve");
            Console.WriteLine("5. Restart FlightPig");
            Console.WriteLine();
            Console.WriteLine("Press any key to open the download page in your browser...");
            Console.ReadKey();

            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    Process.Start(new ProcessStartInfo("https://ollama.com/download") { UseShellExecute = true });
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    Process.Start("open", "https://ollama.com/download");
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    Process.Start("xdg-open", "https://ollama.com/download");
                }
            }
            catch
            {
                Console.WriteLine("Could not open browser. Please manually visit: https://ollama.com/download");
            }
        }
    }
}
