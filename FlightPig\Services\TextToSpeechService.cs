using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using NAudio.Wave;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Text-to-speech service using ElevenLabs API
    /// </summary>
    public class TextToSpeechService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly AppSettings _settings;
        private WaveOutEvent _waveOut;
        private readonly object _lockObject = new object();

        public TextToSpeechService(HttpClient httpClient, AppSettings settings)
        {
            _httpClient = httpClient;
            _settings = settings;
            
            if (!string.IsNullOrEmpty(_settings.ElevenLabs.ApiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("xi-api-key", _settings.ElevenLabs.ApiKey);
            }
        }

        /// <summary>
        /// Speak text using the default voice
        /// </summary>
        public async Task SpeakAsync(string text, bool isPilotVoice = false)
        {
            if (!_settings.ElevenLabs.Enabled || string.IsNullOrEmpty(text))
            {
                Console.WriteLine($"TTS: {text}"); // Fallback to console output
                return;
            }

            var voiceId = isPilotVoice ? _settings.ElevenLabs.PilotVoiceId : _settings.ElevenLabs.DefaultVoiceId;
            await SpeakWithVoiceAsync(text, voiceId);
        }

        /// <summary>
        /// Speak text using a specific voice
        /// </summary>
        public async Task SpeakWithVoiceAsync(string text, string voiceId)
        {
            if (!_settings.ElevenLabs.Enabled || string.IsNullOrEmpty(_settings.ElevenLabs.ApiKey))
            {
                Console.WriteLine($"TTS: {text}"); // Fallback to console output
                return;
            }

            try
            {
                Console.WriteLine($"🔊 Speaking: \"{text}\"");
                
                var audioData = await GenerateSpeechAsync(text, voiceId);
                if (audioData != null)
                {
                    await PlayAudioAsync(audioData);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TTS Error: {ex.Message}");
                Console.WriteLine($"TTS Fallback: {text}"); // Fallback to console output
            }
        }

        /// <summary>
        /// Generate speech audio data from text
        /// </summary>
        private async Task<byte[]> GenerateSpeechAsync(string text, string voiceId)
        {
            try
            {
                var requestBody = new
                {
                    text = text,
                    model_id = "eleven_monolingual_v1",
                    voice_settings = new
                    {
                        stability = _settings.ElevenLabs.Stability,
                        similarity_boost = _settings.ElevenLabs.SimilarityBoost
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var url = $"{_settings.ElevenLabs.BaseUrl}/text-to-speech/{voiceId}";
                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsByteArrayAsync();
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"ElevenLabs API Error: {response.StatusCode} - {errorContent}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating speech: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Play audio data through speakers
        /// </summary>
        private async Task PlayAudioAsync(byte[] audioData)
        {
            try
            {
                lock (_lockObject)
                {
                    _waveOut?.Stop();
                    _waveOut?.Dispose();
                }

                using (var audioStream = new MemoryStream(audioData))
                using (var reader = new Mp3FileReader(audioStream))
                {
                    var waveOut = new WaveOutEvent();
                    
                    lock (_lockObject)
                    {
                        _waveOut = waveOut;
                    }

                    waveOut.Volume = (float)_settings.Voice.Volume;
                    waveOut.Init(reader);
                    waveOut.Play();

                    // Wait for playback to complete
                    while (waveOut.PlaybackState == PlaybackState.Playing)
                    {
                        await Task.Delay(100);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error playing audio: {ex.Message}");
            }
            finally
            {
                lock (_lockObject)
                {
                    _waveOut?.Dispose();
                    _waveOut = null;
                }
            }
        }

        /// <summary>
        /// Speak mission information with appropriate voice
        /// </summary>
        public async Task SpeakMissionAsync(Mission mission)
        {
            if (mission == null) return;

            var missionText = $"New mission: {mission.Title}. {mission.Description}";
            await SpeakAsync(missionText, isPilotVoice: true);

            if (mission.Objectives?.Count > 0)
            {
                await Task.Delay(500); // Brief pause
                
                var objectiveText = $"You have {mission.Objectives.Count} objectives:";
                for (int i = 0; i < mission.Objectives.Count; i++)
                {
                    objectiveText += $" {i + 1}. {mission.Objectives[i].Title}.";
                }
                
                await SpeakAsync(objectiveText, isPilotVoice: true);
            }
        }

        /// <summary>
        /// Speak objective completion
        /// </summary>
        public async Task SpeakObjectiveCompletedAsync(Objective objective)
        {
            if (objective == null) return;

            var text = $"Objective completed: {objective.Title}";
            await SpeakAsync(text, isPilotVoice: true);
        }

        /// <summary>
        /// Speak aircraft status information
        /// </summary>
        public async Task SpeakAircraftStatusAsync(AircraftInfo aircraftInfo)
        {
            var statusText = $"Aircraft status: {aircraftInfo.Title}. " +
                           $"Altitude {aircraftInfo.Altitude:F0} feet. " +
                           $"Airspeed {aircraftInfo.AirspeedKnots:F0} knots. " +
                           $"Heading {aircraftInfo.Heading:F0} degrees. " +
                           $"{(aircraftInfo.OnGround ? "On ground" : "In flight")}.";

            await SpeakAsync(statusText, isPilotVoice: true);
        }

        /// <summary>
        /// Speak confirmation messages
        /// </summary>
        public async Task SpeakConfirmationAsync(string message)
        {
            await SpeakAsync(message, isPilotVoice: false);
        }

        /// <summary>
        /// Speak error messages
        /// </summary>
        public async Task SpeakErrorAsync(string error)
        {
            var errorText = $"Error: {error}";
            await SpeakAsync(errorText, isPilotVoice: false);
        }

        /// <summary>
        /// Speak tour introduction with aircraft-specific context
        /// </summary>
        public async Task SpeakTourIntroductionAsync(Tour tour, string aircraftTitle = null)
        {
            if (tour == null) return;

            var aircraftType = DetermineAircraftType(aircraftTitle);
            var aircraftSpecificIntro = GetAircraftSpecificIntroduction(aircraftType);

            var introText = $"Welcome to {tour.Title}. {aircraftSpecificIntro} {tour.Introduction}";
            await SpeakAsync(introText, isPilotVoice: false); // Use tour guide voice

            if (!string.IsNullOrEmpty(tour.Description))
            {
                await Task.Delay(500);
                await SpeakAsync(tour.Description, isPilotVoice: false);
            }

            // Announce first POI with aircraft-specific guidance
            if (tour.PointsOfInterest?.Count > 0)
            {
                await Task.Delay(1000);
                var firstPoi = tour.PointsOfInterest[0];
                var aircraftGuidance = GetAircraftSpecificGuidance(aircraftType);
                var firstPoiText = $"Our first destination is {firstPoi.Name}. {aircraftGuidance} {firstPoi.NextPoiInstructions}";
                await SpeakAsync(firstPoiText, isPilotVoice: false);
            }
        }

        /// <summary>
        /// Speak POI information when reached
        /// </summary>
        public async Task SpeakPoiInformationAsync(string poiName, string tourGuideText, string nextPoiInstructions = null)
        {
            if (string.IsNullOrEmpty(tourGuideText)) return;

            // Announce arrival
            var arrivalText = $"We have arrived at {poiName}.";
            await SpeakAsync(arrivalText, isPilotVoice: false);

            await Task.Delay(1000);

            // Speak the detailed tour guide information
            await SpeakAsync(tourGuideText, isPilotVoice: false);

            // Give instructions for next POI if provided
            if (!string.IsNullOrEmpty(nextPoiInstructions))
            {
                await Task.Delay(1500);
                await SpeakAsync(nextPoiInstructions, isPilotVoice: false);
            }
        }

        /// <summary>
        /// Speak tour conclusion
        /// </summary>
        public async Task SpeakTourConclusionAsync(Tour tour)
        {
            if (tour == null) return;

            var conclusionText = !string.IsNullOrEmpty(tour.Conclusion)
                ? tour.Conclusion
                : $"This concludes our tour of {tour.Title}. Thank you for flying with us today!";

            await SpeakAsync(conclusionText, isPilotVoice: false);
        }

        /// <summary>
        /// Speak tour progress update
        /// </summary>
        public async Task SpeakTourProgressAsync(int currentPoi, int totalPois, string nextPoiName)
        {
            var progressText = $"Point of interest {currentPoi} of {totalPois} completed.";

            if (!string.IsNullOrEmpty(nextPoiName))
            {
                progressText += $" Next destination: {nextPoiName}.";
            }

            await SpeakAsync(progressText, isPilotVoice: false);
        }

        /// <summary>
        /// Speak comprehensive landing analysis
        /// </summary>
        public async Task SpeakLandingAnalysisAsync(LandingScore score, string aircraftType)
        {
            if (score == null) return;

            // Overall performance announcement
            var overallText = $"Landing complete. Overall score: {score.OverallScore:F0} percent, grade {score.Grade}.";
            await SpeakAsync(overallText, isPilotVoice: false);
            await Task.Delay(1000);

            // Detailed analysis based on performance
            if (score.Analysis.Crashed)
            {
                await SpeakCrashAnalysisAsync(score.Analysis, aircraftType);
            }
            else
            {
                await SpeakSuccessfulLandingAnalysisAsync(score, aircraftType);
            }

            // Achievements
            if (score.Achievements.Count > 0)
            {
                await Task.Delay(1000);
                var achievementsText = "Achievements earned: " + string.Join(", ", score.Achievements) + ".";
                await SpeakAsync(achievementsText, isPilotVoice: false);
            }

            // Improvement suggestions
            if (score.Improvements.Count > 0)
            {
                await Task.Delay(1500);
                await SpeakAsync("Areas for improvement:", isPilotVoice: false);
                await Task.Delay(500);

                foreach (var improvement in score.Improvements)
                {
                    await SpeakAsync(improvement, isPilotVoice: false);
                    await Task.Delay(800);
                }
            }
        }

        /// <summary>
        /// Speak crash analysis with constructive feedback
        /// </summary>
        private async Task SpeakCrashAnalysisAsync(LandingAnalysis analysis, string aircraftType)
        {
            await SpeakAsync("Unfortunately, the landing resulted in a crash. Let's analyze what happened.", isPilotVoice: false);
            await Task.Delay(1000);

            var crashFactors = new List<string>();

            if (analysis.TouchdownSpeedKnots > GetMaxSafeSpeed(aircraftType))
            {
                crashFactors.Add($"excessive approach speed of {analysis.TouchdownSpeedKnots:F0} knots");
            }

            if (Math.Abs(analysis.VerticalSpeedFpm) > 800)
            {
                crashFactors.Add($"hard impact with vertical speed of {Math.Abs(analysis.VerticalSpeedFpm):F0} feet per minute");
            }

            if (analysis.OffRunway)
            {
                crashFactors.Add("landing off the runway centerline");
            }

            if (crashFactors.Any())
            {
                var factorsText = "Contributing factors include: " + string.Join(", ", crashFactors) + ".";
                await SpeakAsync(factorsText, isPilotVoice: false);
                await Task.Delay(1000);
            }

            // Provide specific advice based on aircraft type
            var adviceText = GetCrashRecoveryAdvice(aircraftType);
            await SpeakAsync(adviceText, isPilotVoice: false);
        }

        /// <summary>
        /// Speak successful landing analysis with detailed feedback
        /// </summary>
        private async Task SpeakSuccessfulLandingAnalysisAsync(LandingScore score, string aircraftType)
        {
            // Approach analysis
            if (score.ApproachScore >= 90)
            {
                await SpeakAsync("Excellent approach stability and speed control.", isPilotVoice: false);
            }
            else if (score.ApproachScore >= 70)
            {
                await SpeakAsync("Good approach with room for improvement in stability.", isPilotVoice: false);
            }
            else
            {
                await SpeakAsync("The approach needs work. Focus on maintaining consistent speed and descent rate.", isPilotVoice: false);
            }
            await Task.Delay(800);

            // Touchdown analysis
            if (score.TouchdownScore >= 90)
            {
                var touchdownText = $"Outstanding touchdown at {score.Analysis.TouchdownDistanceFeet:F0} feet from the threshold.";
                if (score.Analysis.SmoothTouchdown)
                {
                    touchdownText += " The landing was smooth as silk.";
                }
                await SpeakAsync(touchdownText, isPilotVoice: false);
            }
            else if (score.TouchdownScore >= 70)
            {
                await SpeakAsync($"Good touchdown at {score.Analysis.TouchdownDistanceFeet:F0} feet from threshold, with minor technique improvements needed.", isPilotVoice: false);
            }
            else
            {
                var issues = new List<string>();
                if (score.Analysis.LongLanding)
                    issues.Add("landing too far down the runway");
                if (score.Analysis.HardLanding)
                    issues.Add("hard touchdown");

                var issuesText = "Touchdown needs improvement: " + string.Join(" and ", issues) + ".";
                await SpeakAsync(issuesText, isPilotVoice: false);
            }
            await Task.Delay(800);

            // Rollout analysis
            if (score.RolloutScore >= 85)
            {
                await SpeakAsync("Excellent directional control during rollout.", isPilotVoice: false);
            }
            else
            {
                await SpeakAsync("Work on maintaining centerline tracking during rollout.", isPilotVoice: false);
            }

            // Aircraft-specific feedback
            await Task.Delay(1000);
            var aircraftAdvice = GetAircraftSpecificLandingAdvice(aircraftType, score);
            if (!string.IsNullOrEmpty(aircraftAdvice))
            {
                await SpeakAsync(aircraftAdvice, isPilotVoice: false);
            }
        }

        /// <summary>
        /// Get aircraft-specific crash recovery advice
        /// </summary>
        private string GetCrashRecoveryAdvice(string aircraftType)
        {
            return aircraftType switch
            {
                "Helicopter" => "For helicopter landings, practice autorotations and focus on gentle collective application during the flare. Maintain rotor RPM and use smooth control inputs.",
                "Jet" => "For jet aircraft, ensure proper approach speed management and use of spoilers. Practice stabilized approaches and go-around procedures when unstable.",
                "Glider" => "For gliders, focus on energy management and proper use of spoilers. Practice spot landings and maintain proper approach angles.",
                "Seaplane" => "For seaplane operations, consider water conditions and practice water landings. Maintain proper attitude and speed for water contact.",
                _ => "Focus on stabilized approaches, proper flare technique, and maintaining centerline tracking. Practice makes perfect - try again!"
            };
        }

        /// <summary>
        /// Get aircraft-specific landing advice based on performance
        /// </summary>
        private string GetAircraftSpecificLandingAdvice(string aircraftType, LandingScore score)
        {
            return aircraftType switch
            {
                "Helicopter" when score.TouchdownScore < 80 => "For helicopters, practice hovering over the landing spot before touchdown. Use collective smoothly to control descent rate.",
                "Jet" when score.ApproachScore < 80 => "For jets, maintain consistent approach speeds and use proper thrust management. Consider using autothrottle if available.",
                "Glider" when score.OverallScore >= 90 => "Excellent glider technique! Your energy management and spot landing skills are impressive.",
                "Seaplane" when score.TouchdownScore >= 85 => "Great water landing technique! Your speed and attitude control were spot on.",
                _ when score.OverallScore >= 95 => "Outstanding piloting skills! That was a textbook landing.",
                _ => ""
            };
        }

        /// <summary>
        /// Get maximum safe landing speed for aircraft type
        /// </summary>
        private double GetMaxSafeSpeed(string aircraftType)
        {
            return aircraftType switch
            {
                "Jet" => 160,
                "Turboprop" => 120,
                "Helicopter" => 80,
                "Glider" => 70,
                "Seaplane" => 90,
                _ => 100 // General Aviation
            };
        }

        /// <summary>
        /// Test TTS with a sample message
        /// </summary>
        public async Task TestSpeechAsync()
        {
            await SpeakAsync("FlightPig text-to-speech is working correctly.", isPilotVoice: false);
            await Task.Delay(1000);
            await SpeakAsync("This is the pilot voice speaking.", isPilotVoice: true);
        }

        /// <summary>
        /// Stop any currently playing audio
        /// </summary>
        public void StopSpeaking()
        {
            lock (_lockObject)
            {
                _waveOut?.Stop();
            }
        }

        /// <summary>
        /// Determine aircraft type based on aircraft title
        /// </summary>
        private string DetermineAircraftType(string aircraftTitle)
        {
            if (string.IsNullOrEmpty(aircraftTitle))
                return "General Aviation";

            var title = aircraftTitle.ToLower();

            if (title.Contains("helicopter") || title.Contains("heli"))
                return "Helicopter";
            if (title.Contains("jet") || title.Contains("boeing") || title.Contains("airbus"))
                return "Jet";
            if (title.Contains("turboprop") || title.Contains("king air"))
                return "Turboprop";
            if (title.Contains("glider") || title.Contains("sailplane"))
                return "Glider";
            if (title.Contains("seaplane") || title.Contains("floatplane"))
                return "Seaplane";

            return "General Aviation";
        }

        /// <summary>
        /// Get aircraft-specific introduction text
        /// </summary>
        private string GetAircraftSpecificIntroduction(string aircraftType)
        {
            return aircraftType switch
            {
                "Helicopter" => "Today we'll be taking advantage of your helicopter's unique capabilities for low-altitude sightseeing and precision flying.",
                "Jet" => "We'll be conducting a high-altitude tour that showcases the speed and range capabilities of your jet aircraft.",
                "Turboprop" => "Your turboprop aircraft is perfect for this medium-altitude tour that balances efficiency with detailed sightseeing.",
                "Glider" => "We'll be using thermal currents and ridge lift to explore the area in your glider, focusing on soaring opportunities.",
                "Seaplane" => "Your amphibious aircraft opens up unique opportunities to explore both land and water features on this tour.",
                _ => "Your general aviation aircraft is well-suited for this scenic tour of the local area."
            };
        }

        /// <summary>
        /// Get aircraft-specific flight guidance
        /// </summary>
        private string GetAircraftSpecificGuidance(string aircraftType)
        {
            return aircraftType switch
            {
                "Helicopter" => "Take your time and use your hover capability to get the best views.",
                "Jet" => "Maintain efficient cruise speeds and altitudes for optimal fuel consumption.",
                "Turboprop" => "Use moderate climb and descent rates for passenger comfort.",
                "Glider" => "Look for thermal activity and ridge lift to maintain altitude.",
                "Seaplane" => "Be aware of water landing opportunities along the route.",
                _ => "Maintain standard VFR procedures and enjoy the scenery."
            };
        }

        /// <summary>
        /// Provide bush flying challenge briefing
        /// </summary>
        public async Task ProvideBushFlyingBriefingAsync(BushFlyingChallenge challenge)
        {
            var briefing = GenerateBushFlyingBriefing(challenge);
            await SpeakAsync(briefing, isPilotVoice: false);
        }

        /// <summary>
        /// Provide bush flying leg briefing
        /// </summary>
        public async Task ProvideBushFlyingLegBriefingAsync(BushFlyingWaypoint waypoint, string aircraftType)
        {
            var briefing = GenerateBushFlyingLegBriefing(waypoint, aircraftType);
            await SpeakAsync(briefing, isPilotVoice: false);
        }

        /// <summary>
        /// Provide bush flying leg completion analysis
        /// </summary>
        public async Task ProvideBushFlyingLegAnalysisAsync(BushFlyingLegResult legResult, BushFlyingWaypoint waypoint)
        {
            var analysis = GenerateBushFlyingLegAnalysis(legResult, waypoint);
            await SpeakAsync(analysis, isPilotVoice: false);
        }

        /// <summary>
        /// Provide bush flying challenge completion summary
        /// </summary>
        public async Task ProvideBushFlyingCompletionAsync(BushFlyingChallenge challenge)
        {
            var summary = GenerateBushFlyingCompletion(challenge);
            await SpeakAsync(summary, isPilotVoice: false);
        }

        /// <summary>
        /// Generate bush flying challenge briefing
        /// </summary>
        private string GenerateBushFlyingBriefing(BushFlyingChallenge challenge)
        {
            var briefing = $"Welcome to your bush flying adventure: {challenge.Title}. ";
            briefing += $"{challenge.Scenario} ";
            briefing += $"This {challenge.Difficulty.ToString().ToLower()} difficulty mission will take you through {challenge.Waypoints.Count} waypoints ";
            briefing += $"covering {challenge.TotalDistanceNM:F0} nautical miles in {challenge.TerrainType.ToString().ToLower()} terrain. ";

            if (challenge.RequiredSkills.Any())
            {
                briefing += $"You'll need to demonstrate {string.Join(", ", challenge.RequiredSkills.Take(3))}. ";
            }

            if (challenge.Challenges.Any())
            {
                briefing += $"Expect challenges including {string.Join(", ", challenge.Challenges.Take(2))}. ";
            }

            briefing += $"Weather conditions are {challenge.WeatherConditions.ToLower()}. ";
            briefing += "Remember, bush flying requires careful planning, conservative decision making, and respect for the terrain. ";
            briefing += "Your safety and that of any passengers depends on your skill and judgment. Good luck!";

            return briefing;
        }

        /// <summary>
        /// Generate bush flying leg briefing
        /// </summary>
        private string GenerateBushFlyingLegBriefing(BushFlyingWaypoint waypoint, string aircraftType)
        {
            var briefing = $"Next waypoint: {waypoint.Name}. ";
            briefing += $"{waypoint.Description} ";

            if (waypoint.DistanceFromPreviousNM > 0)
            {
                briefing += $"Distance: {waypoint.DistanceFromPreviousNM:F0} nautical miles on heading {waypoint.HeadingFromPrevious:F0}. ";
            }

            briefing += $"{waypoint.TerrainDescription} ";

            if (waypoint.LocalChallenges.Any())
            {
                briefing += $"Local challenges include {string.Join(" and ", waypoint.LocalChallenges.Take(2))}. ";
            }

            if (!string.IsNullOrEmpty(waypoint.ApproachNotes))
            {
                briefing += $"Approach notes: {waypoint.ApproachNotes} ";
            }

            if (!string.IsNullOrEmpty(waypoint.LandingNotes))
            {
                briefing += $"Landing notes: {waypoint.LandingNotes} ";
            }

            if (waypoint.LocalHazards.Any())
            {
                briefing += $"Be aware of {string.Join(" and ", waypoint.LocalHazards.Take(2))}. ";
            }

            briefing += GetBushFlyingAircraftAdvice(aircraftType, waypoint);

            return briefing;
        }

        /// <summary>
        /// Generate bush flying leg analysis
        /// </summary>
        private string GenerateBushFlyingLegAnalysis(BushFlyingLegResult legResult, BushFlyingWaypoint waypoint)
        {
            var analysis = $"Leg to {waypoint.Name} completed. ";
            analysis += $"Flight time: {legResult.FlightTimeMinutes:F1} minutes. ";
            analysis += $"Fuel used: {legResult.FuelUsedGallons:F1} gallons. ";
            analysis += $"Overall bush flying score: {legResult.Score.OverallScore:F0} percent, grade {legResult.Score.Grade}. ";

            // Highlight strengths
            if (legResult.Score.Strengths.Any())
            {
                analysis += $"Strengths demonstrated: {string.Join(", ", legResult.Score.Strengths.Take(2))}. ";
            }

            // Provide specific feedback
            if (legResult.Score.NavigationAccuracy >= 85)
            {
                analysis += "Excellent navigation accuracy. ";
            }
            else if (legResult.Score.NavigationAccuracy < 70)
            {
                analysis += "Work on navigation precision and course tracking. ";
            }

            if (legResult.Score.FuelManagement >= 85)
            {
                analysis += "Outstanding fuel management. ";
            }
            else if (legResult.Score.FuelManagement < 70)
            {
                analysis += "Monitor fuel consumption more carefully. ";
            }

            if (legResult.Score.TerrainAwareness >= 85)
            {
                analysis += "Great terrain awareness and altitude management. ";
            }

            // Areas for improvement
            if (legResult.Score.AreasForImprovement.Any())
            {
                analysis += $"Areas to focus on: {string.Join(" and ", legResult.Score.AreasForImprovement.Take(2))}. ";
            }

            analysis += GetBushFlyingEncouragement(legResult.Score.OverallScore);

            return analysis;
        }

        /// <summary>
        /// Generate bush flying challenge completion summary
        /// </summary>
        private string GenerateBushFlyingCompletion(BushFlyingChallenge challenge)
        {
            var overallScore = challenge.CompletedLegs.Average(l => l.Score.OverallScore);
            var totalTime = challenge.CompletedLegs.Sum(l => l.FlightTimeMinutes);
            var totalFuel = challenge.CompletedLegs.Sum(l => l.FuelUsedGallons);

            var summary = $"Bush flying challenge {challenge.Title} completed! ";
            summary += $"You successfully navigated {challenge.CompletedLegs.Count} legs through {challenge.TerrainType.ToString().ToLower()} terrain. ";
            summary += $"Total flight time: {totalTime:F0} minutes. Total fuel consumed: {totalFuel:F1} gallons. ";
            summary += $"Overall performance: {overallScore:F0} percent. ";

            if (overallScore >= 90)
            {
                summary += "Outstanding bush flying performance! You demonstrated exceptional skills in navigation, terrain awareness, and fuel management. ";
                summary += "You're ready for the most challenging bush flying adventures. ";
            }
            else if (overallScore >= 80)
            {
                summary += "Excellent bush flying skills! You showed good judgment and technique throughout the mission. ";
                summary += "Continue practicing to refine your skills further. ";
            }
            else if (overallScore >= 70)
            {
                summary += "Good bush flying performance. You completed the mission safely with room for improvement. ";
                summary += "Focus on navigation accuracy and fuel management for better results. ";
            }
            else
            {
                summary += "Bush flying mission completed, but there's significant room for improvement. ";
                summary += "Practice basic bush flying skills including navigation, terrain awareness, and conservative decision making. ";
            }

            summary += $"Thank you for flying with FlightPig bush flying adventures. Safe flying!";

            return summary;
        }

        /// <summary>
        /// Get aircraft-specific bush flying advice
        /// </summary>
        private string GetBushFlyingAircraftAdvice(string aircraftType, BushFlyingWaypoint waypoint)
        {
            var advice = aircraftType switch
            {
                "General Aviation" => "Use short field techniques and maintain good situational awareness. ",
                "Turboprop" => "Your turboprop's power and performance are well-suited for bush operations. ",
                "Helicopter" => "Take advantage of your helicopter's unique capabilities for precision approaches. ",
                _ => ""
            };

            // Add runway-specific advice
            var runway = waypoint.Airport?.Runways?.FirstOrDefault();
            if (runway != null)
            {
                if (runway.LengthFeet < 3000)
                {
                    advice += "Short runway requires precise speed control and maximum braking. ";
                }
                if (runway.Surface.ToLower().Contains("grass"))
                {
                    advice += "Grass runway may be soft - use appropriate soft field techniques. ";
                }
            }

            return advice;
        }

        /// <summary>
        /// Get encouragement based on performance
        /// </summary>
        private string GetBushFlyingEncouragement(double score)
        {
            if (score >= 95) return "Exceptional bush flying! You're operating at a professional level. ";
            if (score >= 85) return "Great bush flying skills! Keep up the excellent work. ";
            if (score >= 75) return "Good bush flying performance. You're developing solid skills. ";
            if (score >= 65) return "Adequate performance. Continue practicing to improve your bush flying abilities. ";
            return "Keep practicing! Bush flying skills develop with experience and careful attention to technique. ";
        }

        public void Dispose()
        {
            lock (_lockObject)
            {
                _waveOut?.Stop();
                _waveOut?.Dispose();
                _waveOut = null;
            }
        }
    }
}
