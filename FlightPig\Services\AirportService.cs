using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Service for finding and managing airports for landing challenges
    /// </summary>
    public class AirportService
    {
        private readonly List<Airport> _airports;
        private readonly string _airportsDataPath;

        public AirportService(string airportsDataPath = "airports.json")
        {
            _airportsDataPath = airportsDataPath;
            _airports = new List<Airport>();
        }

        /// <summary>
        /// Load airports from JSON file or create default set
        /// </summary>
        public async Task LoadAirportsAsync()
        {
            try
            {
                if (File.Exists(_airportsDataPath))
                {
                    var json = await File.ReadAllTextAsync(_airportsDataPath);
                    var airports = JsonSerializer.Deserialize<List<Airport>>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _airports.Clear();
                    if (airports != null)
                    {
                        _airports.AddRange(airports);
                        Console.WriteLine($"Loaded {airports.Count} airports from {_airportsDataPath}");
                    }
                }
                else
                {
                    await CreateDefaultAirportsAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading airports: {ex.Message}");
                await CreateDefaultAirportsAsync();
            }
        }

        /// <summary>
        /// Find the nearest suitable airport for landing challenge
        /// </summary>
        public Airport FindNearestAirport(AircraftInfo aircraftInfo, double maxDistanceNm = 50)
        {
            var aircraftType = DetermineAircraftType(aircraftInfo.Title);
            
            var suitableAirports = _airports.Where(airport =>
            {
                // Check if airport is suitable for aircraft type
                if (!airport.IsSuitableForAircraft(aircraftType))
                    return false;

                // Calculate distance
                var distance = CalculateDistanceNm(
                    aircraftInfo.Latitude, aircraftInfo.Longitude,
                    airport.Latitude, airport.Longitude);

                return distance <= maxDistanceNm;
            }).ToList();

            if (!suitableAirports.Any())
                return null;

            // Return the nearest airport
            return suitableAirports.OrderBy(airport =>
                CalculateDistanceNm(
                    aircraftInfo.Latitude, aircraftInfo.Longitude,
                    airport.Latitude, airport.Longitude)).First();
        }

        /// <summary>
        /// Find nearby airports within specified radius
        /// </summary>
        public async Task<List<Airport>> FindNearbyAirportsAsync(double latitude, double longitude, double maxDistanceNm = 50)
        {
            return await Task.FromResult(_airports.Where(airport =>
            {
                var distance = CalculateDistanceNm(latitude, longitude, airport.Latitude, airport.Longitude);
                return distance <= maxDistanceNm;
            }).OrderBy(airport => CalculateDistanceNm(latitude, longitude, airport.Latitude, airport.Longitude))
            .ToList());
        }

        /// <summary>
        /// Get airports suitable for aircraft type within range
        /// </summary>
        public List<Airport> GetSuitableAirports(AircraftInfo aircraftInfo, double maxDistanceNm = 50)
        {
            var aircraftType = DetermineAircraftType(aircraftInfo.Title);
            
            return _airports.Where(airport =>
            {
                if (!airport.IsSuitableForAircraft(aircraftType))
                    return false;

                var distance = CalculateDistanceNm(
                    aircraftInfo.Latitude, aircraftInfo.Longitude,
                    airport.Latitude, airport.Longitude);

                return distance <= maxDistanceNm;
            }).OrderBy(airport =>
                CalculateDistanceNm(
                    aircraftInfo.Latitude, aircraftInfo.Longitude,
                    airport.Latitude, airport.Longitude)).ToList();
        }

        /// <summary>
        /// Generate a landing challenge for the nearest suitable airport
        /// </summary>
        public LandingChallenge CreateLandingChallenge(AircraftInfo aircraftInfo)
        {
            var airport = FindNearestAirport(aircraftInfo);
            if (airport == null)
                return null;

            var runway = airport.GetPrimaryRunway();
            if (runway == null)
                return null;

            var aircraftType = DetermineAircraftType(aircraftInfo.Title);
            var distance = CalculateDistanceNm(
                aircraftInfo.Latitude, aircraftInfo.Longitude,
                airport.Latitude, airport.Longitude);

            var challenge = new LandingChallenge
            {
                Id = Guid.NewGuid().ToString(),
                Title = $"Landing Challenge: {airport.Name}",
                Description = $"Execute a precision landing at {airport.Name} ({airport.IcaoCode}) on runway {runway.Designation}. Distance: {distance:F1} NM",
                Airport = airport,
                TargetRunway = runway,
                AircraftType = aircraftType,
                Difficulty = airport.Difficulty,
                WeatherConditions = "Clear",
                ApproachType = runway.HasILS ? ApproachType.ILS : ApproachType.Visual,
                IsActive = false,
                Score = new LandingScore()
            };

            return challenge;
        }

        /// <summary>
        /// Calculate distance between two points in nautical miles
        /// </summary>
        private double CalculateDistanceNm(double lat1, double lon1, double lat2, double lon2)
        {
            const double earthRadiusNm = 3440.065;
            
            var lat1Rad = lat1 * Math.PI / 180;
            var lat2Rad = lat2 * Math.PI / 180;
            var deltaLatRad = (lat2 - lat1) * Math.PI / 180;
            var deltaLonRad = (lon2 - lon1) * Math.PI / 180;

            var a = Math.Sin(deltaLatRad / 2) * Math.Sin(deltaLatRad / 2) +
                    Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                    Math.Sin(deltaLonRad / 2) * Math.Sin(deltaLonRad / 2);
            
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            
            return earthRadiusNm * c;
        }

        /// <summary>
        /// Determine aircraft type for airport suitability
        /// </summary>
        private string DetermineAircraftType(string aircraftTitle)
        {
            if (string.IsNullOrEmpty(aircraftTitle))
                return "General Aviation";

            var title = aircraftTitle.ToLower();

            if (title.Contains("helicopter") || title.Contains("heli"))
                return "Helicopter";
            if (title.Contains("jet") || title.Contains("boeing") || title.Contains("airbus"))
                return "Jet";
            if (title.Contains("turboprop") || title.Contains("king air"))
                return "Turboprop";
            if (title.Contains("glider") || title.Contains("sailplane"))
                return "Glider";
            if (title.Contains("seaplane") || title.Contains("floatplane"))
                return "Seaplane";

            return "General Aviation";
        }

        /// <summary>
        /// Create a minimal fallback airport set if the main database fails to load
        /// </summary>
        private Task CreateDefaultAirportsAsync()
        {
            _airports.Clear();
            Console.WriteLine("Creating minimal fallback airport set...");

            // Add just a few major airports as fallback
            var fallbackAirports = new List<Airport>
            {
                new Airport
                {
                    IcaoCode = "KSFO",
                    IataCode = "SFO",
                    Name = "San Francisco International Airport",
                    Latitude = 37.6213,
                    Longitude = -122.3790,
                    ElevationFeet = 13,
                    Type = AirportType.International,
                    Country = "United States",
                    Region = "California",
                    City = "San Francisco",
                    IsControlled = true,
                    HasILS = true,
                    Difficulty = LandingDifficulty.Medium,
                    SuitableAircraft = new List<string> { "Jet", "Turboprop", "General Aviation" },
                    Description = "Major international airport",
                    Runways = new List<Runway>
                    {
                        new Runway
                        {
                            Designation = "28L",
                            Heading = 284,
                            LengthFeet = 11870,
                            WidthFeet = 200,
                            Surface = "Asphalt",
                            HasLights = true,
                            HasILS = true
                        }
                    }
                },
                new Airport
                {
                    IcaoCode = "EGLL",
                    IataCode = "LHR",
                    Name = "London Heathrow Airport",
                    Latitude = 51.4706,
                    Longitude = -0.4619,
                    ElevationFeet = 83,
                    Type = AirportType.International,
                    Country = "United Kingdom",
                    City = "London",
                    IsControlled = true,
                    HasILS = true,
                    Difficulty = LandingDifficulty.Medium,
                    SuitableAircraft = new List<string> { "Jet", "Turboprop", "General Aviation" },
                    Description = "Major international airport",
                    Runways = new List<Runway>
                    {
                        new Runway
                        {
                            Designation = "09L",
                            Heading = 87,
                            LengthFeet = 12799,
                            WidthFeet = 164,
                            Surface = "Asphalt",
                            HasLights = true,
                            HasILS = true
                        }
                    }
                }
            };

            _airports.AddRange(fallbackAirports);
            Console.WriteLine($"Created {fallbackAirports.Count} fallback airports");
            return Task.CompletedTask;
        }

        /// <summary>
        /// Save airports to JSON file
        /// </summary>
        private async Task SaveAirportsAsync()
        {
            try
            {
                var json = JsonSerializer.Serialize(_airports, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
                await File.WriteAllTextAsync(_airportsDataPath, json);
                Console.WriteLine($"Saved {_airports.Count} airports to {_airportsDataPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving airports: {ex.Message}");
            }
        }
    }
}
